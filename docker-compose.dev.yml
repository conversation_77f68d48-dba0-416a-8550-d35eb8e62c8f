version: "3.8"

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    image: gw-crm-api:dev
    ports:
      - "9091:9999"
    env_file:
      - .env
    healthcheck:
      test: [CMD, wget, --spider, -q, "http://0.0.0.0:9999/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    networks:
      - api-network

networks:
  api-network:
    driver: bridge
