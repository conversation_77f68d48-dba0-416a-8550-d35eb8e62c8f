import { cors } from "hono/cors";
import { notFound, serveEmojiFavicon } from "stoker/middlewares";

import { configureOpenAPI } from "@/lib/openapi";
import { TelegramBotService } from "@/lib/telegram/bot-service";
import { authMiddleware } from "@/middlewares/auth.middleware";
import { errorHandler } from "@/middlewares/error-handler.middleware";
import { pinoLogger } from "@/middlewares/pino-logger.middleware";
import { securityHeaders } from "@/middlewares/security.middleware";
import telegramTest from "@/routes/test/telegram";
import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { createMessageObjectSchema } from "stoker/openapi/schemas";
import env from "./env";
import { configureAuth } from "./lib/auth";
import { API_TAGS } from "./lib/constants";
import { createRouter, jsonContent } from "./lib/helpers";
import routers from "./routes";

export function createApp() {
  const app = createRouter();

  if (!env.isTest) {
    TelegramBotService.startScheduledTasks();
  }

  const trustedOrigins: string[] = [
    "https://crm-group-working.com",
    "https://www.crm-group-working.com",

    "https://group-working.com",
    "https://www.group-working.com",

    "http://localhost:5173",
    "http://localhost:3000",

    "https://darkslateblue-hare-523682.hostingersite.com",
  ];
  if (env.isDevelopment) {
    trustedOrigins.push("http://localhost:3000");
    trustedOrigins.push("http://localhost:9999");
    trustedOrigins.push("http://localhost:5173");
  }

  app.use(
    "*",
    cors({
      origin: trustedOrigins,
      allowHeaders: ["Content-Type", "Authorization"],
      allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
      exposeHeaders: ["Content-Length"],
      maxAge: 600,
      credentials: true,
    }),
  );
  app.use(serveEmojiFavicon("📄"));
  app.use(pinoLogger());
  app.use(securityHeaders);
  app.use("*", authMiddleware);
  app.notFound(notFound);
  app.onError(errorHandler);

  return app;
}

const app = createApp()
  .get(
    "/",
    describeRoute({
      title: "Health Check",
      tags: [API_TAGS.SYSTEM],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          createMessageObjectSchema("Group Working CRM API"),
          "Group Working CRM API Index",
        ),
      },
    }),
    (c) => {
      return c.json(
        {
          ok: true,
          message: "Group Working CRM API",
        },
        HttpStatusCodes.OK,
      );
    },
  )
  .route("/job-vacancies", routers.jobVacancies)
  .route("/applications", routers.applications)
  .route("/candidates", routers.candidates)
  .route("/users", routers.users)
  .route("/authors", routers.authors);

if (env.isDevelopment) {
  app.route("/test/telegram", telegramTest);
}

configureAuth(app);
configureOpenAPI(app);

export type AppType = typeof app;
export default app;
