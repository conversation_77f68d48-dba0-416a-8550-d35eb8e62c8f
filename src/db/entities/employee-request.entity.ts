import { integer, pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { generateId } from "@/lib/helpers";
import { jobVacancy } from "./job-vacancy.entity";
import { user } from "./user.entity";

export const EMPLOYEE_REQUEST_STATUSES = [
  "new",
  "in_progress", 
  "duplicate",
  "not_relevant",
  "finished",
] as const;

export const employeeRequest = pgTable("employee_request", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),
  
  name: text("name").notNull(),
  phoneNumber: text("phone_number").notNull(),
  companyActivity: text("company_activity").notNull(),
  
  employeeCount: integer("employee_count"),
  question: text("question"),
  comment: text("comment"),
  
  status: text("status", { enum: EMPLOYEE_REQUEST_STATUSES })
    .notNull()
    .default("new"),
  
  
  jobVacancyId: text("job_vacancy_id").references(() => jobVacancy.id, {
    onDelete: "set null",
  }),
  responsibleManagerId: text("responsible_manager_id").references(
    () => user.id,
    {
      onDelete: "set null",
    },
  ),
  createdByUserId: text("created_by_user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  
  
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const insertEmployeeRequestSchema = createInsertSchema(employeeRequest);
export const selectEmployeeRequestSchema = createSelectSchema(employeeRequest);
export const patchEmployeeRequestSchema = selectEmployeeRequestSchema
  .partial()
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export type EmployeeRequest = typeof employeeRequest.$inferSelect;
export type NewEmployeeRequest = typeof employeeRequest.$inferInsert;
