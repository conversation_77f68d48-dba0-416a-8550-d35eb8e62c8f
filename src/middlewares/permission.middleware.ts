import { auth } from "@/lib/auth";
import type { Permissions } from "@/lib/auth/roles";
import { AppError, ErrorCode } from "@/lib/errors";
import type { Context, Next } from "hono";

export function permissionGuard(permission: Permissions) {
  return async (c: Context, next: Next) => {
    const { success } = await auth.api.userHasPermission({
      body: {
        userId: c.var.checkedUser?.id,
        permission,
      },
    });

    if (!success) {
      throw new AppError(ErrorCode.FORBIDDEN, "Forbidden");
    }

    return next();
  };
}
