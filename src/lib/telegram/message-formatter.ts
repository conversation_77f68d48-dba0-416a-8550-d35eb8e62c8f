import type { JobVacancy } from "@/db/entities/job-vacancy.entity";
import type { JobWithLanguageReqs } from "@/db/schema";
import { TranslationService } from "@/lib/translations/service";
import type { SupportedLanguage } from "../constants";
import { COUNTRY_FLAGS, TELEGRAM_CONFIG } from "./config";

function formatSalaryRange(
  salaryFrom?: number | null,
  salaryTo?: number | null,
): string {
  if (!salaryFrom && !salaryTo) {
    return "To be negotiated";
  }
  if (salaryFrom && salaryTo) {
    return `${salaryFrom} - ${salaryTo}`;
  }
  if (salaryFrom) {
    return `From ${salaryFrom}`;
  }
  if (salaryTo) {
    return `Up to ${salaryTo}`;
  }
  return "To be negotiated";
}

export async function formatVacancyMessage(
  vacancy: JobWithLanguageReqs,
  language: SupportedLanguage = "ua",
): Promise<string> {
  const countryEmoji = vacancy.country
    ? COUNTRY_FLAGS[vacancy.country] || "✈️"
    : "✈️";

  const title = await TranslationService.getTranslation(
    vacancy.titleTextId,
    language,
  );

  const responsibilities = await TranslationService.getTranslation(
    vacancy.responsibilitiesTextId,
    language,
  );
  const workSchedule = await TranslationService.getTranslation(
    vacancy.workScheduleTextId,
    language,
  );

  // Add housing information
  const housingInfo = vacancy.housing
    ? getHousingText(vacancy.housing, language)
    : "";

  return `*🔔 Job Vacancy: ${title}*\n
*Code:* \`${vacancy.slug}\`
*👥 Positions Available:* ${vacancy.numberOfPositions}\n
🌎*${countryEmoji}Country:* ${vacancy.country}
*🌆Cities:* ${vacancy.cities.join(", ")}\n
*🔑Responsibilities:* ${responsibilities}
*💶Salary:* ${formatSalaryRange(vacancy.salaryFrom, vacancy.salaryTo)}
*💡Experience:* ${vacancy.experience}\n
*🏠Housing:* ${housingInfo}\n
*🗓Work Schedule:* ${workSchedule}\n
*📚Language:* ${vacancy.languageRequirements
    .map((lr) =>
      lr.level !== "none" ? `${lr.language} - ${lr.level} level` : lr.language,
    )
    .join(", ")}\n
${vacancy.notes ? `*➕Additional Info:* ${vacancy.notes}\n` : ""} 
 📞 *Contact:* [Click here](https://t.me/groupworkingpl)
    `;
}
export type VacancyWithMessage = JobVacancy & {
  messageId?: number;
};

export function formatVacancyList(vacancies: VacancyWithMessage[]): string {
  const currentDate = new Date().toLocaleDateString();

  return `📝Available Vacancies (${currentDate})\n\n ${vacancies
    .map((v) => {
      const countryEmoji = v.country ? COUNTRY_FLAGS[v.country] || "✈️" : "✈️";
      const salaryText = formatSalaryRange(v.salaryFrom, v.salaryTo);
      return `[${countryEmoji} ${v.slug}. ${salaryText}](https://t.me/${TELEGRAM_CONFIG.channelId.split("@")?.[1]}/${v.messageId})\n`;
    })
    .join("\n")}`;
}

// TODO change
function getHousingText(housing: string, language: SupportedLanguage): string {
  if (language === "ua") {
    switch (housing) {
      case "provided":
        return "Надається";
      case "not_provided":
        return "Не надається";
      case "partially_compensated":
        return "Частково компенсується";
      default:
        return "";
    }
  }

  switch (housing) {
    case "provided":
      return "Provided";
    case "not_provided":
      return "Not provided";
    case "partially_compensated":
      return "Partially compensated";
    default:
      return "";
  }
}
