import type { Context } from "hono";

import * as HttpStatusCodes from "stoker/http-status-codes";

import { ErrorResponseDTO } from "./errors";
import { jsonContent } from "./helpers";
import {
  forbiddenSchema,
  notFoundSchema,
  unauthorizedSchema,
  unprocessableEntitySchema,
} from "./schemas";

export const responses = {
  noContent(c: Context) {
    return c.body(null, HttpStatusCodes.NO_CONTENT);
  },
};

export function commonErrorResponses(...codes: number[]) {
  const map: Record<number, ReturnType<typeof jsonContent>> = {
    401: jsonContent(ErrorResponseDTO, "Unauthorized"),
    403: json<PERSON>ontent(ErrorResponseDTO, "Forbidden"),
    404: jsonContent(ErrorResponseDTO, "Not found"),
    422: jsonContent(ErrorResponseDTO, "Unprocessable Entity"),
    409: jsonContent(ErrorResponseDTO, "Conflict"),
    429: jsonContent(ErrorResponseDTO, "Too Many Requests"),
    500: jsonContent(ErrorResponseDTO, "Internal error"),
  };
  return Object.fromEntries(codes.map((code) => [code, map[code]]));
}

export const openApiResponses = {
  unauthorized: {
    [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
      unauthorizedSchema,
      "Unauthorized",
    ),
  },
  notFound: {
    [HttpStatusCodes.NOT_FOUND]: jsonContent(notFoundSchema, "Not Found"),
  },
  unprocessableEntity: {
    [HttpStatusCodes.UNPROCESSABLE_ENTITY]: jsonContent(
      unprocessableEntitySchema,
      "Unprocessable Entity",
    ),
  },
  forbidden: {
    [HttpStatusCodes.FORBIDDEN]: jsonContent(forbiddenSchema, "Forbidden"),
  },
  conflict: {
    [HttpStatusCodes.CONFLICT]: jsonContent(
      unprocessableEntitySchema,
      "Conflict",
    ),
  },
  tooManyRequests: {
    [HttpStatusCodes.TOO_MANY_REQUESTS]: jsonContent(
      unprocessableEntitySchema,
      "Too Many Requests",
    ),
  },
};
