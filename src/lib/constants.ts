import { z } from "zod";

export const SUPPORTED_LANGUAGES = ["ua", "en", "ru"] as const;
export type SupportedLanguage = (typeof SUPPORTED_LANGUAGES)[number];

export const REQUIREMENTS_LANGUAGES = ["en", "de", "fr", "other"] as const;
export type RequirementsLanguage = (typeof REQUIREMENTS_LANGUAGES)[number];

export const COUNTRY_CODES = ["BE", "FR", "AT", "UA", "NL", "DE"] as const;
export type CountryCode = (typeof COUNTRY_CODES)[number];

export const API_COUNTRY_CODES = [
  null,
  "BE",
  "FR",
  "AT",
  "UA",
  "NL",
  "DE",
] as const;
export type CountryCodes = (typeof API_COUNTRY_CODES)[number];

export const GENDERS = ["male", "female", "pair", "brigade", "any"] as const;

export const SPECIALTY_CODES = [
  "others",
  "loader",
  "weaver",
  "allrounder",
  "plasterer",
  "packer",
  "housekeeper",
  "borer",
  "rebar",
  "handyman",
  "carpenter",
  "turner",
  "engineer",
  "guardian",
  "plumber",
  "chambermaid",
  "builder",
  "seamstress",
  "equipmentoperator",
  "nanny",
  "nurse",
  "electrician",
  "welder",
  "driver",
  "mounter",
  "cleaning",
  "locksmith",
  "formwork",
  "concreter",
  "crane",
  "stacker",
  "millingmachiner",
  "grinder",
  "roofer",
  "stonemason",
  "tiler",
  "drywallinstaller",
  "facadespecialist",
  "automechanic",
  "interiorfinisher",
  "painter",
  "salesperson",
  "solarpanels",
  "gascutter",
  "cncoperator",
  "governess",
  "dismantling",
  "waiter",
  "furnituremaker",
  "metallurgist",
  "mechanic",
  "meatdeboner",
  "gardener",
  "tirefitter",
  "excavatoroperator",
  "driverb",
  "driverce",
  "dumptruck",
  "loaderdriver",
  "trucker",
  "electricalengineer",
  "tractordriver",
  "mechanicfitter",
  "bulldozerdriver",
  "manager",
] as const;

export type SpecialtyCode = (typeof SPECIALTY_CODES)[number];

export const specialtyCodeSchema = z.enum(SPECIALTY_CODES);

export const WORKPLACE_CODES = [
  "building",
  "factory",
  "warehouse",
  "hotel",
  "agro",
  "shop",
  "meetplant",
  "clothingwarehouse",
  "furniturefactory",
  "supermarket",
  "socialsphere",
  "restaurant",
  "milkaplant",
  "volkswagenplant",
  "carpentryshop",
  "airconditioner",
  "monolith",
  "logging",
  "scaffolding",
  "plant",
  "production",
  "confectionery",
  "carfactory",
  "farms",
  "greenhouse",
  "groupworking",
] as const;

export type WorkplaceCode = (typeof WORKPLACE_CODES)[number];

export const ZOD_ERROR_MESSAGES = {
  REQUIRED: "Required",
  EXPECTED_NUMBER: "Expected number, received nan",
  NO_UPDATES: "No updates provided",
};

export const ZOD_ERROR_CODES = {
  INVALID_UPDATES: "invalid_updates",
};

export const API_TAGS = {
  SYSTEM: "System",
  AUTH: "Authentication",
  JOB_VACANCIES: "Job Vacancies",
  JOB_APPLICATIONS: "Job Applications",
  CANDIDATES: "Candidates",
  AUTHORS: "Authors",
  USERS: "Users",
} as const;

export const API_TAGS_ORDER = [
  { name: API_TAGS.SYSTEM, description: "System-wide endpoints" },
  { name: API_TAGS.JOB_VACANCIES, description: "Job vacancy endpoints" },
  {
    name: API_TAGS.JOB_APPLICATIONS,
    description: "Job applications endpoints",
  },
  { name: API_TAGS.CANDIDATES, description: "Candidates endpoints" },
  { name: API_TAGS.AUTHORS, description: "Authors endpoints" },
  { name: API_TAGS.USERS, description: "User management endpoints" },
  { name: API_TAGS.AUTH, description: "Authentication endpoints" },
] as const;
