import env from "@/env";
import { logger } from "@/middlewares/pino-logger.middleware";
import Redis from "ioredis";

let redisClient: Redis | null = null;

export function getRedisClient(): Redis | null {
  if (!redisClient) {
    try {
      redisClient = new Redis({
        host: env.REDIS_HOST,
        port: env.REDIS_PORT,
        password: env.REDIS_PASSWORD,
        keyPrefix: "gw-crm:",
      });
      redisClient.on("error", (err) => {
        logger.error(err, "Redis error");
      });
    } catch (err) {
      logger.error(err, "Failed to connect to <PERSON><PERSON>");
    }
  }
  return redisClient;
}
