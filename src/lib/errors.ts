import type { ContentfulStatusCode } from "hono/utils/http-status";
import { z } from "zod";

// --- Error Code Enum ---
export enum ErrorCode {
  USER_NOT_FOUND = "USER_NOT_FOUND",
  VACANCY_NOT_FOUND = "VACANCY_NOT_FOUND",
  CANDIDATE_NOT_FOUND = "CANDIDATE_NOT_FOUND",
  APPLICATION_NOT_FOUND = "APPLICATION_NOT_FOUND",
  AUTHOR_NOT_FOUND = "AUTHOR_NOT_FOUND",
  FORBIDDEN = "FORBIDDEN",
  UNAUTHORIZED = "UNAUTHORIZED",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  CONFLICT = "CONFLICT",
  RATE_LIMIT = "RATE_LIMIT",
  BAD_REQUEST = "BAD_REQUEST",
  INTERNAL_ERROR = "INTERNAL_ERROR",
  INVALID_JOB_VACANCY_ID = "INVALID_JOB_VACANCY_ID",
  INVALID_MANAGER_ID = "INVALID_MANAGER_ID",
}

// --- Error Response DTO ---
export const ErrorResponseDTO = z.object({
  code: z.nativeEnum(ErrorCode),
  message: z.string(),
  details: z.any().optional(),
});
export type ErrorResponse = z.infer<typeof ErrorResponseDTO>;

// --- AppError Class ---
export class AppError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: unknown,
  ) {
    super(message);
    this.name = "AppError";
  }
}

// --- Helper: Map ErrorCode to HTTP Status ---
export function mapErrorCodeToStatus(code: ErrorCode): number {
  if (code.endsWith("_NOT_FOUND")) return 404;
  switch (code) {
    case ErrorCode.FORBIDDEN:
      return 403;
    case ErrorCode.UNAUTHORIZED:
      return 401;
    case ErrorCode.VALIDATION_ERROR:
    case ErrorCode.BAD_REQUEST:
      return 400;
    case ErrorCode.CONFLICT:
      return 409;
    case ErrorCode.RATE_LIMIT:
      return 429;
    case ErrorCode.INVALID_MANAGER_ID:
    case ErrorCode.INVALID_JOB_VACANCY_ID:
      return 422;
    default:
      return 500;
  }
}

// --- DEPRECATED: APIError (for backward compatibility) ---
/**
 * @deprecated Use AppError and ErrorCode instead.
 */
export class APIError extends Error {
  constructor(
    public status: ContentfulStatusCode,
    public body?: {
      message?: string;
      code?: string;
      [key: string]: unknown;
    },
    public headers: HeadersInit = {},
    public statusCode: ContentfulStatusCode = status,
  ) {
    super(body?.message || "API Error");
    this.name = "APIError";
  }
}
