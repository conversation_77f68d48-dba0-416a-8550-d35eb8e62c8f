import {
  APPLICATION_SOURCES,
  APPLICATION_STATUSES,
  APPLICATION_TYPES,
  selectCandidateSchema,
  selectJobApplicationSchema,
  selectUserSchema,
} from "@/db/schema";
import { createPaginatedSchema, paginationParamsSchema } from "@/lib/schemas";
import { z } from "zod";

export const createJobApplicationDTO = z.object({
  jobVacancyId: z.string().min(1).optional(),
  citizenship: z.string().min(1).optional(),
  fullName: z.string().min(1),
  phoneNumber: z.string().min(1),
  status: z.enum(APPLICATION_STATUSES).optional(),
  applicationType: z.enum(APPLICATION_TYPES),
  source: z.enum(APPLICATION_SOURCES).optional().nullable(),
  vacancyLink: z.string().optional(),
  referral: z.string().optional(),
  comment: z.string().optional(),
  responsibleManagerId: z.string().optional(),
});

export const jobApplicationWithCandidateDTO = selectJobApplicationSchema
  .omit({
    candidateId: true,
    responsibleManagerId: true,
  })
  .extend({
    isForeign: z.boolean().optional(),
    candidate: selectCandidateSchema.nullable(),
    responsibleManager: selectUserSchema
      .pick({
        id: true,
        name: true,
        image: true,
      })
      .nullable(),
  });

export const listApplicationsDTO = createPaginatedSchema(
  jobApplicationWithCandidateDTO,
);

export type ListApplicationsDTO = z.infer<typeof listApplicationsDTO>;

export const listApplicationsQueryDTO = paginationParamsSchema.extend({
  sortBy: z.enum(["createdAt", "updatedAt"]).optional(),
  sortDirection: z.enum(["asc", "desc"]).optional(),
  status: z.enum(APPLICATION_STATUSES).optional(),
  applicationType: z.enum(APPLICATION_TYPES).optional(),
  source: z.enum(APPLICATION_SOURCES).optional().nullable(),
  responsibleManagerId: z.string().optional(),
  search: z.string().optional(), // Search in fullName, phoneNumber, citizenship
  dateFrom: z.string().optional(), // Filter by createdDate
  dateTo: z.string().optional(),
});

export const applicationStatsDTO = z.object({
  totalApplications: z.number(),
  byStatus: z.record(z.string(), z.number()),
  byType: z.record(z.string(), z.number()),
  bySource: z.record(z.string(), z.number()),
  recentApplications: z.number(), // Last 7 days
});

export const bulkUpdateApplicationsDTO = z.object({
  applicationIds: z.array(z.string().min(1)),
  updates: z.object({
    status: z.enum(APPLICATION_STATUSES).optional(),
    responsibleManagerId: z.string().optional(),
    comment: z.string().optional(),
  }),
});

export type CreateJobApplicationDTO = z.infer<typeof createJobApplicationDTO>;
export type JobApplicationWithCandidate = z.infer<
  typeof jobApplicationWithCandidateDTO
>;
