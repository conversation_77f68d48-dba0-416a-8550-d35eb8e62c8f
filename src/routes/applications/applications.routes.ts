import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";

import db from "@/db";
import {
  patchJobApplicationSchema,
  selectJobApplicationSchema,
} from "@/db/schema";
import { API_TAGS } from "@/lib/constants";
import { AppError, ErrorCode, mapErrorCodeToStatus } from "@/lib/errors";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { commonErrorResponses } from "@/lib/responses";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { rateLimit } from "@/middlewares/rate-limit.middleware";
import type { ContentfulStatusCode } from "hono/utils/http-status";
import { PublicJobDTO } from "../job-vacancies/job-vacancies.dtos";
import { JobVacancyOperations } from "../job-vacancies/job-vacancies.operations";
import {
  applicationStatsDTO,
  bulkUpdateApplicationsDTO,
  createJobApplicationDTO,
  jobApplicationWithCandidateDTO,
  listApplicationsDTO,
  listApplicationsQueryDTO,
} from "./applications.dtos";
import { JobApplicationOperations } from "./applications.operations";

const router = createRouter()
  .get(
    "/",
    authGuard,
    describeRoute({
      title: "List Job Applications",
      description:
        "List all job applications.\n- Access: Authenticated users with the 'jobApplication:read' permission (admin, editor, author).\n- Admins: Can view all applications.\n- Editors: Can view applications where they are the responsible manager.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          listApplicationsDTO,
          "List of job applications",
        ),
        ...commonErrorResponses(401, 403, 500),
      },
    }),
    permissionGuard({ jobApplication: ["read"] }),
    validator("query", listApplicationsQueryDTO.optional()),
    async (c) => {
      try {
        const query = c.req.valid("query");
        const applications = await JobApplicationOperations.findMany(
          c.var.checkedUser.id,
          c.var.checkedUser.role,
          query,
        );
        return c.json(applications, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/:id",
    authGuard,
    describeRoute({
      title: "Get Job Application",
      description:
        "Get a specific job application by ID.\n- Access: Authenticated users with the 'jobApplication:read' permission (admin, editor, author).\n- Admins: Can view any application.\n- Editors: Can view applications where they are the responsible manager.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          jobApplicationWithCandidateDTO
            .omit({ jobVacancyId: true })
            .extend({ jobVacancy: PublicJobDTO.nullable() }),
          "Single job application with candidate and vacancy details",
        ),
        ...commonErrorResponses(401, 403, 404, 500),
      },
    }),
    permissionGuard({ jobApplication: ["read"] }),
    async (c) => {
      try {
        const { id } = c.req.param();
        const userId = c.var.checkedUser.id;
        const isAdminOrEditor =
          c.var.checkedUser.role === "admin" ||
          c.var.checkedUser.role === "editor";
        const application = await JobApplicationOperations.findByIdWithVacancy(
          id,
          isAdminOrEditor,
          userId,
        );

        if (!application) {
          return c.json(
            {
              code: ErrorCode.APPLICATION_NOT_FOUND,
              message: `Job application with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.json(application, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )

  .get(
    "/stats",
    authGuard,
    describeRoute({
      title: "Get Application Statistics",
      description:
        "Get statistics about job applications.\n- Access: Authenticated users with the 'jobApplication:stats' permission (admin, editor, author).\n- Admins: See stats for all applications.\n- Editors: See stats for applications where they are the responsible manager.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          applicationStatsDTO,
          "Application statistics",
        ),
        ...commonErrorResponses(401, 403, 500),
      },
    }),
    permissionGuard({ jobApplication: ["stats"] }),
    async (c) => {
      try {
        const stats = await JobApplicationOperations.getStats(
          c.var.checkedUser.id,
          c.var.checkedUser.role,
        );
        return c.json(stats, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .post(
    "/",
    rateLimit({
      max: 1,
      window: "5m",
    }),
    describeRoute({
      title: "Create Job Application",
      description:
        "Create a new job application.\n- Public endpoint: No authentication required.\n- Rate-limited (1 request per 5 minutes per user/IP).\n- Automatically matches or creates a candidate based on provided info.\n- Returns the created application and candidate data.\n- Validates job vacancy and manager IDs if provided.",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          jobApplicationWithCandidateDTO,
          "Created job application with candidate data",
        ),
        ...commonErrorResponses(429, 422, 500),
      },
    }),
    validator("json", createJobApplicationDTO),
    async (c) => {
      try {
        const data = c.req.valid("json");
        const userId = c.var.checkedUser.id;
        const isAdminOrEditor =
          c.var.checkedUser.role === "admin" ||
          c.var.checkedUser.role === "editor";
        // Validate job vacancy if provided
        if (data.jobVacancyId) {
          const vacancy = await JobVacancyOperations.findById(
            data.jobVacancyId,
            isAdminOrEditor,
            userId,
          );
          if (!vacancy) {
            return c.json(
              {
                code: ErrorCode.INVALID_JOB_VACANCY_ID,
                message: `Job vacancy with ID ${data.jobVacancyId} not found`,
              },
              422 as ContentfulStatusCode,
            );
          }
        }

        if (data.responsibleManagerId) {
          const manager = await db.query.user.findFirst({
            where: {
              id: data.responsibleManagerId,
            },
          });
          if (!manager) {
            return c.json(
              {
                code: ErrorCode.INVALID_MANAGER_ID,
                message: `Manager with ID ${data.responsibleManagerId} not found`,
              },
              422 as ContentfulStatusCode,
            );
          }
        }

        const application =
          await JobApplicationOperations.createWithCandidateData(data);

        return c.json(application, HttpStatusCodes.CREATED);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .patch(
    "/:id",
    authGuard,
    describeRoute({
      title: "Update Job Application",
      description:
        "Update a job application by ID.\n- Access: Authenticated users with the 'jobApplication:update' permission (admin, editor, author).\n- Admins and editors: Can update any application.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectJobApplicationSchema,
          "Updated job application",
        ),
        ...commonErrorResponses(401, 403, 404, 422, 500),
      },
    }),
    permissionGuard({ jobApplication: ["update"] }),
    validator("json", patchJobApplicationSchema),
    async (c) => {
      try {
        const { id } = c.req.param();
        const updates = c.req.valid("json");
        if (c.var.checkedUser.role === "author") {
          const applicationWithVacancy =
            await JobApplicationOperations.findByIdWithRawVacancy(id);
          if (
            applicationWithVacancy?.jobVacancy?.authorId !==
            c.var.checkedUser.id
          ) {
            return c.json(
              {
                code: ErrorCode.FORBIDDEN,
                message: "Forbidden",
              },
              403 as ContentfulStatusCode,
            );
          }
        }
        if (updates.responsibleManagerId) {
          const manager = await db.query.user.findFirst({
            where: {
              id: updates.responsibleManagerId,
            },
          });
          if (!manager) {
            return c.json(
              {
                code: ErrorCode.INVALID_MANAGER_ID,
                message: `Manager with ID ${updates.responsibleManagerId} not found`,
              },
              422 as ContentfulStatusCode,
            );
          }
        }

        const application = await JobApplicationOperations.update(id, updates);
        if (!application) {
          return c.json(
            {
              code: ErrorCode.APPLICATION_NOT_FOUND,
              message: `Job application with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }

        return c.json(application, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .patch(
    "/",
    authGuard,
    describeRoute({
      title: "Bulk Update Applications",
      description:
        "Bulk update multiple job applications.\n- Access: Authenticated users with the 'jobApplication:bulk_update' permission (admin, editor).\n- Admins and editors: Can update any applications.\n- Authors do not have access to this endpoint.\n- Returns the updated applications.",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.array(selectJobApplicationSchema),
          "Updated applications",
        ),
        ...commonErrorResponses(401, 403, 422, 500),
      },
    }),
    permissionGuard({ jobApplication: ["bulk_update"] }),
    validator("json", bulkUpdateApplicationsDTO),
    async (c) => {
      try {
        const { applicationIds, updates } = c.req.valid("json");
        const updatedApplications = await JobApplicationOperations.bulkUpdate(
          applicationIds,
          updates,
        );
        return c.json(updatedApplications, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .delete(
    "/:id",
    authGuard,
    describeRoute({
      title: "Delete Job Application",
      description:
        "Delete a job application by ID.\n- Access: Authenticated users with the 'jobApplication:delete' permission (admin, editor, author).\n- Admins and editors: Can delete any application.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: {
          description: "Job application deleted",
        },
        ...commonErrorResponses(401, 403, 404, 500),
      },
    }),
    permissionGuard({ jobApplication: ["delete"] }),
    async (c) => {
      try {
        const { id } = c.req.param();
        const deleted = await JobApplicationOperations.delete(id);
        if (!deleted) {
          return c.json(
            {
              code: ErrorCode.APPLICATION_NOT_FOUND,
              message: `Job application with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.body(null, HttpStatusCodes.NO_CONTENT);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  );

export default router;
