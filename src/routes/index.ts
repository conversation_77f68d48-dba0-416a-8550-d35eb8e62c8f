import applicationsRouter from "./applications/applications.routes";
import authorsRouter from "./authors/authors.routes";
import candidatesRouter from "./candidates/candidates.routes";
import employeeRequestsRouter from "./employee-requests/employee-requests.routes";
import jobVacanciesRouter from "./job-vacancies/job-vacancies.routes";
import usersRouter from "./users/users.routes";

const routers = {
  jobVacancies: jobVacanciesRouter,
  applications: applicationsRouter,
  candidates: candidatesRouter,
  employeeRequests: employeeRequestsRouter,
  authors: authorsRouter,
  users: usersRouter,
};

export default routers;
