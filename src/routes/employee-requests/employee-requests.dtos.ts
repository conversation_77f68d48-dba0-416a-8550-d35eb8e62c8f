import {
  EMPLOYEE_REQUEST_STATUSES,
  insertEmployeeRequestSchema,
  selectEmployeeRequestSchema,
} from "@/db/schema";
import {
  createPaginatedSchema,
  paginationParamsSchema,
} from "@/lib/schemas";
import { z } from "zod";

export const createEmployeeRequestDTO = insertEmployeeRequestSchema
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
    status: true,
    createdByUserId: true,
  })
  .extend({
    name: z.string().min(1, "Name is required"),
    phoneNumber: z.string().min(1, "Phone number is required"),
    companyActivity: z.string().min(1, "Company activity is required"),
    employeeCount: z.number().int().min(1).optional(),
    question: z.string().optional(),
    comment: z.string().optional(),
    jobVacancyId: z.string().optional(),
    responsibleManagerId: z.string().optional(),
  });

export const updateEmployeeRequestDTO = selectEmployeeRequestSchema
  .partial()
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
    createdByUserId: true,
  });

export const employeeRequestDTO = selectEmployeeRequestSchema.extend({
  responsibleManager: z
    .object({
      id: z.string(),
      name: z.string(),
      image: z.string().nullable(),
    })
    .optional()
    .nullable(),
  createdByUser: z.object({
    id: z.string(),
    name: z.string(),
    image: z.string().nullable(),
  }),
  jobVacancy: z
    .object({
      id: z.string(),
      sequence: z.number(),
      slug: z.string(),
    })
    .optional()
    .nullable(),
});

export const limitedEmployeeRequestDTO = selectEmployeeRequestSchema
  .pick({
    id: true,
    companyActivity: true,
    employeeCount: true,
    question: true,
    jobVacancyId: true,
    status: true,
    createdAt: true,
  })
  .extend({
    jobVacancy: z
      .object({
        id: z.string(),
        sequence: z.number(),
        slug: z.string(),
      })
      .optional()
      .nullable(),
  });

export const listEmployeeRequestsDTO = createPaginatedSchema(employeeRequestDTO);
export const listLimitedEmployeeRequestsDTO = createPaginatedSchema(
  limitedEmployeeRequestDTO,
);

export const listEmployeeRequestsQueryDTO = paginationParamsSchema.extend({
  search: z.string().optional(),
  status: z
    .string()
    .optional()
    .describe("Comma-separated list of statuses to filter by")
    .transform((val) => {
      if (!val) return undefined;
      const statuses = val
        .split(",")
        .map((s) => s.trim())
        .filter(Boolean);
      for (const status of statuses) {
        if (!(EMPLOYEE_REQUEST_STATUSES as readonly string[]).includes(status)) {
          throw new Error(
            `Invalid status: ${status}. Must be one of: ${EMPLOYEE_REQUEST_STATUSES.join(", ")}`,
          );
        }
      }
      return statuses.length > 0 ? statuses : undefined;
    }),
  responsibleManagerId: z.string().optional(),
  jobVacancyId: z.string().optional(),
  createdByUserId: z.string().optional(),
});

export const employeeRequestFilterSchema = z.object({
  search: z.string().optional(),
  status: z.array(z.enum(EMPLOYEE_REQUEST_STATUSES)).optional(),
  responsibleManagerId: z.string().optional(),
  jobVacancyId: z.string().optional(),
  createdByUserId: z.string().optional(),
});

export type CreateEmployeeRequestDTO = z.infer<typeof createEmployeeRequestDTO>;
export type UpdateEmployeeRequestDTO = z.infer<typeof updateEmployeeRequestDTO>;
export type EmployeeRequestDTO = z.infer<typeof employeeRequestDTO>;
export type LimitedEmployeeRequestDTO = z.infer<typeof limitedEmployeeRequestDTO>;
export type ListEmployeeRequestsQueryDTO = z.infer<typeof listEmployeeRequestsQueryDTO>;
export type EmployeeRequestFilterDTO = z.infer<typeof employeeRequestFilterSchema>;
