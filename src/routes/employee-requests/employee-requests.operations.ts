import db from "@/db";
import { type EmployeeRequest, employeeRequest } from "@/db/schema";
import { AppError, ErrorCode } from "@/lib/errors";
import { and, count, eq, ilike, inArray, or } from "drizzle-orm";
import type {
  CreateEmployeeRequestDTO,
  EmployeeRequestDTO,
  EmployeeRequestFilterDTO,
  LimitedEmployeeRequestDTO,
  UpdateEmployeeRequestDTO,
} from "./employee-requests.dtos";

export class EmployeeRequestOperations {
  static async list(
    filters: EmployeeRequestFilterDTO = {},
    page = 1,
    limit = 10,
    sortBy: "createdAt" | "updatedAt" = "createdAt",
    sortDirection: "asc" | "desc" = "desc",
    includePrivateInfo = false,
  ): Promise<{
    items: EmployeeRequestDTO[] | LimitedEmployeeRequestDTO[];
    total: number;
    page: number;
    limit: number;
    pages: number;
  }> {
    const offset = (page - 1) * limit;
    const conditions = [];

    // Build filter conditions
    if (filters.search) {
      conditions.push(
        or(
          ilike(employeeRequest.name, `%${filters.search}%`),
          ilike(employeeRequest.companyActivity, `%${filters.search}%`),
          ilike(employeeRequest.question, `%${filters.search}%`),
        ),
      );
    }

    if (filters.status?.length) {
      conditions.push(inArray(employeeRequest.status, filters.status));
    }

    if (filters.responsibleManagerId) {
      conditions.push(
        eq(employeeRequest.responsibleManagerId, filters.responsibleManagerId),
      );
    }

    if (filters.jobVacancyId) {
      conditions.push(eq(employeeRequest.jobVacancyId, filters.jobVacancyId));
    }

    if (filters.createdByUserId) {
      conditions.push(
        eq(employeeRequest.createdByUserId, filters.createdByUserId),
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    const [{ total }] = await db
      .select({ total: count() })
      .from(employeeRequest)
      .where(whereClause);

    const items = await db.query.employeeRequest.findMany({
      where: whereClause,
      limit,
      offset,
      orderBy: (employeeRequest, { asc, desc }) => [
        sortDirection === "asc"
          ? asc(employeeRequest[sortBy])
          : desc(employeeRequest[sortBy]),
      ],
      with: includePrivateInfo
        ? {
            responsibleManager: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
            createdByUser: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
            jobVacancy: {
              columns: {
                id: true,
                sequence: true,
                slug: true,
              },
            },
          }
        : {
            jobVacancy: {
              columns: {
                id: true,
                sequence: true,
                slug: true,
              },
            },
          },
    });

    const pages = Math.ceil(total / limit);

    if (includePrivateInfo) {
      return {
        items: items as EmployeeRequestDTO[],
        total,
        page,
        limit,
        pages,
      };
    } else {
      // Return limited fields for authors
      const limitedItems = items.map((item) => ({
        id: item.id,
        companyActivity: item.companyActivity,
        employeeCount: item.employeeCount,
        question: item.question,
        jobVacancyId: item.jobVacancyId,
        status: item.status,
        createdAt: item.createdAt,
        jobVacancy: item.jobVacancy,
      })) as LimitedEmployeeRequestDTO[];

      return {
        items: limitedItems,
        total,
        page,
        limit,
        pages,
      };
    }
  }

  static async findById(
    id: string,
    includePrivateInfo = false,
  ): Promise<EmployeeRequestDTO | LimitedEmployeeRequestDTO | null> {
    const request = await db.query.employeeRequest.findFirst({
      where: eq(employeeRequest.id, id),
      with: includePrivateInfo
        ? {
            responsibleManager: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
            createdByUser: {
              columns: {
                id: true,
                name: true,
                image: true,
              },
            },
            jobVacancy: {
              columns: {
                id: true,
                sequence: true,
                slug: true,
              },
            },
          }
        : {
            jobVacancy: {
              columns: {
                id: true,
                sequence: true,
                slug: true,
              },
            },
          },
    });

    if (!request) {
      return null;
    }

    if (includePrivateInfo) {
      return request as EmployeeRequestDTO;
    } else {
      // Return limited fields for authors
      return {
        id: request.id,
        companyActivity: request.companyActivity,
        employeeCount: request.employeeCount,
        question: request.question,
        jobVacancyId: request.jobVacancyId,
        status: request.status,
        createdAt: request.createdAt,
        jobVacancy: request.jobVacancy,
      } as LimitedEmployeeRequestDTO;
    }
  }

  static async create(
    data: CreateEmployeeRequestDTO,
    createdByUserId: string,
  ): Promise<EmployeeRequest> {
    const [newRequest] = await db
      .insert(employeeRequest)
      .values({
        ...data,
        createdByUserId,
      })
      .returning();

    return newRequest;
  }

  static async update(
    id: string,
    updates: UpdateEmployeeRequestDTO,
    userId?: string,
    isAdminOrEditor?: boolean,
  ): Promise<EmployeeRequest | null> {
    // Check if request exists and user has permission
    const existingRequest = await db.query.employeeRequest.findFirst({
      where: eq(employeeRequest.id, id),
    });

    if (!existingRequest) {
      return null;
    }

    // Only admin/editor can update, or the user who created it
    if (!isAdminOrEditor && existingRequest.createdByUserId !== userId) {
      throw new AppError(
        ErrorCode.FORBIDDEN,
        "You do not have permission to update this employee request",
      );
    }

    const [updated] = await db
      .update(employeeRequest)
      .set(updates)
      .where(eq(employeeRequest.id, id))
      .returning();

    return updated;
  }

  static async delete(
    id: string,
    userId?: string,
    isAdminOrEditor?: boolean,
  ): Promise<boolean> {
    // Check if request exists and user has permission
    const existingRequest = await db.query.employeeRequest.findFirst({
      where: eq(employeeRequest.id, id),
    });

    if (!existingRequest) {
      return false;
    }

    // Only admin/editor can delete, or the user who created it
    if (!isAdminOrEditor && existingRequest.createdByUserId !== userId) {
      throw new AppError(
        ErrorCode.FORBIDDEN,
        "You do not have permission to delete this employee request",
      );
    }

    const [deleted] = await db
      .delete(employeeRequest)
      .where(eq(employeeRequest.id, id))
      .returning();

    return !!deleted;
  }
}
