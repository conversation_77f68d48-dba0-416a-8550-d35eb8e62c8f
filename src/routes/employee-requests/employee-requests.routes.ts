import { API_TAGS } from "@/lib/constants";
import { AppError, ErrorCode, mapErrorCodeToStatus } from "@/lib/errors";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { commonErrorResponses } from "@/lib/responses";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { describeRoute } from "hono-openapi";
import type { ContentfulStatusCode } from "hono/utils/http-status";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";
import {
  createEmployeeRequestDTO,
  employeeRequestDTO,
  limitedEmployeeRequestDTO,
  listEmployeeRequestsDTO,
  listEmployeeRequestsQueryDTO,
  listLimitedEmployeeRequestsDTO,
  updateEmployeeRequestDTO,
} from "./employee-requests.dtos";
import { EmployeeRequestOperations } from "./employee-requests.operations";

const router = createRouter()
  .get(
    "/",
    authGuard,
    describeRoute({
      title: "List Employee Requests",
      description:
        "List all employee requests with optional filtering.\n- Access: Authenticated users with the 'employeeRequest:read' permission (admin, editor, author).\n- Admins and editors: Can view all employee requests with full details.\n- Authors: <AUTHORS>
      tags: [API_TAGS.EMPLOYEE_REQUESTS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.union([listEmployeeRequestsDTO, listLimitedEmployeeRequestsDTO]),
          "List of employee requests",
        ),
        ...commonErrorResponses(401, 403, 500),
      },
    }),
    permissionGuard({ employeeRequest: ["read"] }),
    validator("query", listEmployeeRequestsQueryDTO),
    async (c) => {
      try {
        const user = c.var.user!;
        const isAdminOrEditor = user.role === "admin" || user.role === "editor";
        const query = c.req.valid("query");

        const {
          search,
          status,
          responsibleManagerId,
          jobVacancyId,
          createdByUserId,
          ...pagination
        } = query;

        const filters = {
          search,
          status,
          responsibleManagerId,
          jobVacancyId,
          createdByUserId,
        };

        const result = await EmployeeRequestOperations.list(
          filters,
          pagination.page,
          pagination.limit,
          pagination.sortBy,
          pagination.sortDirection,
          isAdminOrEditor,
        );

        return c.json(result, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/:id",
    authGuard,
    describeRoute({
      title: "Get Employee Request",
      description:
        "Get a specific employee request by ID.\n- Access: Authenticated users with the 'employeeRequest:read' permission (admin, editor, author).\n- Admins and editors: Can view full details.\n- Authors: <AUTHORS>
      tags: [API_TAGS.EMPLOYEE_REQUESTS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.union([employeeRequestDTO, limitedEmployeeRequestDTO]),
          "Employee request details",
        ),
        ...commonErrorResponses(401, 403, 404, 500),
      },
    }),
    permissionGuard({ employeeRequest: ["read"] }),
    async (c) => {
      try {
        const user = c.var.user!;
        const isAdminOrEditor = user.role === "admin" || user.role === "editor";
        const id = c.req.param("id");

        const request = await EmployeeRequestOperations.findById(
          id,
          isAdminOrEditor,
        );

        if (!request) {
          return c.json(
            {
              code: ErrorCode.EMPLOYEE_REQUEST_NOT_FOUND,
              message: "Employee request not found",
            },
            HttpStatusCodes.NOT_FOUND,
          );
        }

        return c.json(request, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .post(
    "/",
    authGuard,
    describeRoute({
      title: "Create Employee Request",
      description:
        "Create a new employee request.\n- Access: Authenticated users with the 'employeeRequest:create' permission (admin, editor).\n- Only admins and editors can create employee requests.\n- The request will be associated with the creating user.",
      tags: [API_TAGS.EMPLOYEE_REQUESTS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          employeeRequestDTO,
          "Created employee request",
        ),
        ...commonErrorResponses(400, 401, 403, 500),
      },
    }),
    permissionGuard({ employeeRequest: ["create"] }),
    validator("json", createEmployeeRequestDTO),
    async (c) => {
      try {
        const user = c.var.user!;
        const data = c.req.valid("json");

        const newRequest = await EmployeeRequestOperations.create(
          data,
          user.id,
        );

        // Fetch the created request with relations
        const createdRequest = await EmployeeRequestOperations.findById(
          newRequest.id,
          true,
        );

        return c.json(createdRequest, HttpStatusCodes.CREATED);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .patch(
    "/:id",
    authGuard,
    describeRoute({
      title: "Update Employee Request",
      description:
        "Update an existing employee request.\n- Access: Authenticated users with the 'employeeRequest:update' permission (admin, editor).\n- Only admins and editors can update employee requests.\n- Users can only update requests they created unless they are admin/editor.",
      tags: [API_TAGS.EMPLOYEE_REQUESTS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          employeeRequestDTO,
          "Updated employee request",
        ),
        ...commonErrorResponses(400, 401, 403, 404, 500),
      },
    }),
    permissionGuard({ employeeRequest: ["update"] }),
    validator("json", updateEmployeeRequestDTO),
    async (c) => {
      try {
        const user = c.var.user!;
        const isAdminOrEditor = user.role === "admin" || user.role === "editor";
        const id = c.req.param("id");
        const updates = c.req.valid("json");

        const updatedRequest = await EmployeeRequestOperations.update(
          id,
          updates,
          user.id,
          isAdminOrEditor,
        );

        if (!updatedRequest) {
          return c.json(
            {
              code: ErrorCode.EMPLOYEE_REQUEST_NOT_FOUND,
              message: "Employee request not found",
            },
            HttpStatusCodes.NOT_FOUND,
          );
        }

        // Fetch the updated request with relations
        const result = await EmployeeRequestOperations.findById(
          updatedRequest.id,
          true,
        );

        return c.json(result, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .delete(
    "/:id",
    authGuard,
    describeRoute({
      title: "Delete Employee Request",
      description:
        "Delete an employee request.\n- Access: Authenticated users with the 'employeeRequest:delete' permission (admin, editor).\n- Only admins and editors can delete employee requests.\n- Users can only delete requests they created unless they are admin/editor.",
      tags: [API_TAGS.EMPLOYEE_REQUESTS],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: {
          description: "Employee request deleted successfully",
        },
        ...commonErrorResponses(401, 403, 404, 500),
      },
    }),
    permissionGuard({ employeeRequest: ["delete"] }),
    async (c) => {
      try {
        const user = c.var.user!;
        const isAdminOrEditor = user.role === "admin" || user.role === "editor";
        const id = c.req.param("id");

        const deleted = await EmployeeRequestOperations.delete(
          id,
          user.id,
          isAdminOrEditor,
        );

        if (!deleted) {
          return c.json(
            {
              code: ErrorCode.APPLICATION_NOT_FOUND,
              message: "Employee request not found",
            },
            HttpStatusCodes.NOT_FOUND,
          );
        }

        return c.body(null, HttpStatusCodes.NO_CONTENT);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  );

export default router;
