import { API_TAGS } from "@/lib/constants";
import {
  AppError,
  ErrorCode,
  ErrorResponseDTO,
  mapErrorCodeToStatus,
} from "@/lib/errors";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { paginationParamsSchema } from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { describeRoute } from "hono-openapi";
import type { ContentfulStatusCode } from "hono/utils/http-status";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";
import { jobApplicationWithCandidateDTO } from "../applications/applications.dtos";
import { JobApplicationOperations } from "../applications/applications.operations";
import {
  candidateWithApplicationsDTO,
  createCandidateDTO,
  listCandidatesDTO,
  listCandidatesQueryDTO,
  mergeCandidatesDTO,
  updateCandidateDTO,
} from "./candidates.dtos";
import { CandidateOperations } from "./candidates.operations";

const router = createRouter()
  .get(
    "/",
    authGuard,
    describeRoute({
      title: "List Candidates",
      description:
        "List all candidates with optional filtering.\n- Access: Authenticated users with the 'candidate:read' permission (admin, editor, author).\n- Admins and editors: Can view all candidates.\n- Authors: <AUTHORS>
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          listCandidatesDTO,
          "List of candidates",
        ),
        [HttpStatusCodes.FORBIDDEN]: jsonContent(ErrorResponseDTO, "Forbidden"),
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ candidate: ["read"] }),
    validator("query", listCandidatesQueryDTO.optional()),
    async (c) => {
      try {
        const query = c.req.valid("query");
        const candidates = await CandidateOperations.findMany(query);
        return c.json(candidates, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/:id",
    authGuard,
    describeRoute({
      title: "Get Candidate",
      description:
        "Get a candidate by ID, including their job applications.\n- Access: Authenticated users with the 'candidate:read' permission (admin, editor, author).\n- Admins and editors: Can view any candidate.\n- Authors: <AUTHORS>
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          candidateWithApplicationsDTO,
          "Candidate with applications",
        ),
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.NOT_FOUND]: jsonContent(ErrorResponseDTO, "Not found"),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ candidate: ["read"] }),
    async (c) => {
      try {
        const { id } = c.req.param();
        const candidate =
          await CandidateOperations.findByIdWithApplications(id);
        if (!candidate) {
          return c.json(
            {
              code: ErrorCode.CANDIDATE_NOT_FOUND,
              message: `Candidate with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.json(candidate, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .post(
    "/",
    authGuard,
    describeRoute({
      title: "Create Candidate",
      description:
        "Create a new candidate.\n- Access: Authenticated users with the 'candidate:create' permission (admin, editor, author).\n- Returns the created candidate with applications (if any).",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          candidateWithApplicationsDTO,
          "Created candidate",
        ),
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.UNPROCESSABLE_ENTITY]: jsonContent(
          ErrorResponseDTO,
          "Unprocessable entity",
        ),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ candidate: ["create"] }),
    validator("json", createCandidateDTO),
    async (c) => {
      try {
        const data = c.req.valid("json");
        const candidate = await CandidateOperations.create(data);
        return c.json(candidate, HttpStatusCodes.CREATED);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .patch(
    "/:id",
    authGuard,
    describeRoute({
      title: "Update Candidate",
      description:
        "Update a candidate by ID.\n- Access: Authenticated users with the 'candidate:update' permission (admin, editor, author).\n- Admins and editors: Can update any candidate.\n- Authors: <AUTHORS>
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          candidateWithApplicationsDTO,
          "Updated candidate",
        ),
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.NOT_FOUND]: jsonContent(ErrorResponseDTO, "Not found"),
        [HttpStatusCodes.UNPROCESSABLE_ENTITY]: jsonContent(
          ErrorResponseDTO,
          "Unprocessable entity",
        ),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ candidate: ["update"] }),
    validator("json", updateCandidateDTO),
    async (c) => {
      try {
        const { id } = c.req.param();
        const updates = c.req.valid("json");
        const candidate = await CandidateOperations.findById(id);
        if (!candidate) {
          return c.json(
            {
              code: ErrorCode.CANDIDATE_NOT_FOUND,
              message: `Candidate with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }
        const updated = await CandidateOperations.update(id, updates);
        return c.json(updated, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .post(
    "/:id/merge",
    authGuard,
    describeRoute({
      title: "Merge Candidates",
      description:
        "Merge another candidate into the specified candidate.\n- Access: Authenticated users with the 'candidate:merge' permission (admin, editor).\n- Transfers all applications and merges phone numbers, then deletes the source candidate.\n- Returns the merged candidate with all applications.\n- Returns 404 or 422 if merge fails or not permitted.",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          candidateWithApplicationsDTO,
          "Merged candidate with all applications",
        ),
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.NOT_FOUND]: jsonContent(ErrorResponseDTO, "Not found"),
        [HttpStatusCodes.UNPROCESSABLE_ENTITY]: jsonContent(
          ErrorResponseDTO,
          "Unprocessable entity",
        ),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ candidate: ["merge"] }),
    validator("json", mergeCandidatesDTO),
    async (c) => {
      try {
        const { id: targetCandidateId } = c.req.param();
        const { sourceCandidateId } = c.req.valid("json");
        const mergedCandidate = await CandidateOperations.mergeCandidates(
          targetCandidateId,
          sourceCandidateId,
        );
        if (!mergedCandidate) {
          return c.json(
            {
              code: ErrorCode.CANDIDATE_NOT_FOUND,
              message: "Failed to merge candidates",
            },
            422 as ContentfulStatusCode,
          );
        }
        return c.json(mergedCandidate, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/:id/applications",
    authGuard,
    describeRoute({
      title: "Get Candidate Applications",
      description:
        "Get all job applications for a specific candidate.\n- Access: Authenticated users with the 'jobApplication:read' permission (admin, editor, author).\n- Admins and editors: Can view applications for any candidate.\n- Authors: <AUTHORS>
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.array(jobApplicationWithCandidateDTO),
          "List of applications for the candidate",
        ),
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.FORBIDDEN]: jsonContent(ErrorResponseDTO, "Forbidden"),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    validator("query", paginationParamsSchema.optional()),
    permissionGuard({ jobApplication: ["read"] }),
    async (c) => {
      try {
        const { id: candidateId } = c.req.param();
        const query = c.req.valid("query");
        const applications =
          await JobApplicationOperations.findManyByCandidateId(
            candidateId,
            c.var.checkedUser.id,
            c.var.checkedUser.role,
            query,
          );
        return c.json(applications, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .delete(
    "/:id",
    authGuard,
    describeRoute({
      title: "Delete Candidate",
      description:
        "Delete a candidate by ID.\n- Access: Authenticated users with the 'candidate:delete' permission (admin, editor, author).\n- Admins and editors: Can delete any candidate.\n- Authors: <AUTHORS>
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: {
          description: "Candidate deleted successfully",
        },
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.NOT_FOUND]: jsonContent(ErrorResponseDTO, "Not found"),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ candidate: ["delete"] }),
    async (c) => {
      try {
        const { id } = c.req.param();
        const candidate = await CandidateOperations.findById(id);
        if (!candidate) {
          return c.json(
            {
              code: ErrorCode.CANDIDATE_NOT_FOUND,
              message: "Candidate not found",
            },
            404 as ContentfulStatusCode,
          );
        }
        await CandidateOperations.delete(id);
        return c.body(null, HttpStatusCodes.NO_CONTENT);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  );

export default router;
