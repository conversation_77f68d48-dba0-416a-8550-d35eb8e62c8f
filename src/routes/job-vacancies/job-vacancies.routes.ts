import { API_TAGS } from "@/lib/constants";
import {
  AppError,
  ErrorCode,
  ErrorResponseDTO,
  mapErrorCodeToStatus,
} from "@/lib/errors";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { createPaginatedSchema, paginationParamsSchema } from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { describeRoute } from "hono-openapi";
import type { ContentfulStatusCode } from "hono/utils/http-status";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";
import { jobApplicationWithCandidateDTO } from "../applications/applications.dtos";
import { JobApplicationOperations } from "../applications/applications.operations";
import {
  JobWithAuthorDTO,
  PublicJobDTO,
  citiesByCountryDTO,
  createJobDTO,
  listJobsQueryDTO,
  // listJobsWithAuthorDTO,
  rawJobResponseDTO,
  updateJobDTO,
} from "./job-vacancies.dtos";
import { JobVacancyOperations } from "./job-vacancies.operations";

const router = createRouter()
  .get(
    "/",
    describeRoute({
      title: "List Job Vacancies",
      description:
        "List all job vacancies.\n- Public/unauthenticated and authors: Only published and archived vacancies are returned, with limited fields (no author or internal details).\n- Admins and editors: Can view all vacancies, including drafts, and receive additional fields such as author information and internal notes.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          createPaginatedSchema(z.union([PublicJobDTO, JobWithAuthorDTO])),
          "List of job vacancies",
        ),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    validator("query", listJobsQueryDTO.optional()),
    async (c) => {
      try {
        const query = c.req.valid("query");
        const user = c.var.user;
        const isAdminOrEditor =
          !!user && (user.role === "admin" || user.role === "editor");
        const vacancies = await JobVacancyOperations.findMany(
          query,
          isAdminOrEditor,
        );
        return c.json(vacancies, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .post(
    "/",
    authGuard,
    describeRoute({
      title: "Create Job Vacancy",
      description:
        "Create a new job vacancy.\n- Access: Only authenticated users with the 'jobVacancy:create' permission (admin, editor, author) can use this endpoint.\n- The created vacancy is associated with the current user as the author.\n- Returns the full internal representation of the created vacancy.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          rawJobResponseDTO,
          "Created job vacancy",
        ),
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.FORBIDDEN]: jsonContent(ErrorResponseDTO, "Forbidden"),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ jobVacancy: ["create"] }),
    validator(
      "json",
      createJobDTO.omit({ authorId: true, editorId: true, isForeign: true }),
    ),
    async (c) => {
      try {
        const data = c.req.valid("json");
        const vacancy = await JobVacancyOperations.create({
          ...data,
          authorId: c.var.checkedUser.id,
          isForeign: c.var.checkedUser.role === "author",
        });
        return c.json(vacancy, HttpStatusCodes.CREATED);
      } catch (err) {
        console.error(err);
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/locations",
    describeRoute({
      title: "Get Cities by Country",
      description:
        "Get a mapping of countries to cities for all published job vacancies.\n- Public endpoint: No authentication required.\n- Only countries with published vacancies are included.\n- Used for filtering and search UI.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          citiesByCountryDTO,
          "Cities grouped by countries",
        ),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    async (c) => {
      try {
        const citiesByCountry = await JobVacancyOperations.getCitiesByCountry();
        return c.json(citiesByCountry, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/:id",
    describeRoute({
      title: "Get Job Vacancy",
      description:
        "Get a specific job vacancy by ID or slug.\n- Public endpoint: Anyone can access published or archived vacancies.\n- Admins and editors: Can access all vacancies, including drafts, and see additional fields (author info, internal notes).\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.union([PublicJobDTO, JobWithAuthorDTO]),
          "Single job vacancy",
        ),
        [HttpStatusCodes.NOT_FOUND]: jsonContent(ErrorResponseDTO, "Not found"),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    async (c) => {
      try {
        const { id } = c.req.param();
        const user = c.var.user;
        const isAdminOrEditor =
          !!user && (user.role === "admin" || user.role === "editor");
        let vacancy = await JobVacancyOperations.findById(
          id,
          isAdminOrEditor,
          user?.id,
        ).catch((err) => {
          if (
            err instanceof AppError &&
            err.code === ErrorCode.VACANCY_NOT_FOUND
          ) {
            return null;
          }
          throw err;
        });

        if (!vacancy) {
          vacancy = await JobVacancyOperations.findBySlug(
            id,
            isAdminOrEditor,
            user?.id,
          );
        }
        if (!vacancy) {
          return c.json(
            {
              code: ErrorCode.VACANCY_NOT_FOUND,
              message: "Job vacancy not found",
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.json(vacancy, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/:id/applications",
    authGuard,
    describeRoute({
      title: "Get Job Applications",
      description:
        "Get all job applications for a specific job vacancy.\n- Access: Authenticated users only.\n- Admins and editors: Can view applications for any vacancy.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          createPaginatedSchema(jobApplicationWithCandidateDTO),
          "Paginated list of job applications",
        ),
        [HttpStatusCodes.FORBIDDEN]: jsonContent(ErrorResponseDTO, "Forbidden"),
        [HttpStatusCodes.NOT_FOUND]: jsonContent(ErrorResponseDTO, "Not found"),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    validator("query", paginationParamsSchema.optional()),
    async (c) => {
      try {
        const { id } = c.req.param();
        const query = c.req.valid("query");
        const vacancy = await JobVacancyOperations.findRawById(id);
        if (!vacancy) {
          return c.json(
            {
              code: ErrorCode.VACANCY_NOT_FOUND,
              message: "Job vacancy not found",
            },
            404 as ContentfulStatusCode,
          );
        }
        if (
          vacancy.authorId !== c.var.checkedUser.id &&
          c.var.checkedUser.role === "author"
        ) {
          return c.json(
            { code: ErrorCode.FORBIDDEN, message: "Forbidden" },
            403 as ContentfulStatusCode,
          );
        }
        const applications = await JobApplicationOperations.findManyByVacancyId(
          id,
          c.var.checkedUser.id,
          c.var.checkedUser.role,
          query,
        );
        return c.json(applications, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .patch(
    "/:id",
    authGuard,
    describeRoute({
      title: "Update Job Vacancy",
      description:
        "Update an existing job vacancy by ID.\n- Access: Only authenticated users with the 'jobVacancy:update' permission (admin, editor, author) can use this endpoint.\n- Admins and editors: Can update any vacancy.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          rawJobResponseDTO,
          "Updated job vacancy",
        ),
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.FORBIDDEN]: jsonContent(ErrorResponseDTO, "Forbidden"),
        [HttpStatusCodes.NOT_FOUND]: jsonContent(ErrorResponseDTO, "Not found"),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ jobVacancy: ["update"] }),
    validator("json", updateJobDTO),
    async (c) => {
      try {
        const { id } = c.req.param();
        const user = c.var.user;
        const isAdminOrEditor =
          !!user && (user.role === "admin" || user.role === "editor");
        const updates = c.req.valid("json");
        const vacancy = await JobVacancyOperations.update(
          id,
          updates,
          user?.id,
          isAdminOrEditor,
        );
        if (!vacancy) {
          return c.json(
            {
              code: ErrorCode.VACANCY_NOT_FOUND,
              message: "Job vacancy not found or forbidden",
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.json(vacancy, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .delete(
    "/:id",
    authGuard,
    describeRoute({
      title: "Delete Job Vacancy",
      description:
        "Delete a job vacancy by ID.\n- Access: Only authenticated users with the 'jobVacancy:delete' permission (admin, editor, author) can use this endpoint.\n- Admins and editors: Can delete any vacancy.\n- Authors: <AUTHORS>
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: { description: "Job vacancy deleted" },
        [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
          ErrorResponseDTO,
          "Unauthorized",
        ),
        [HttpStatusCodes.FORBIDDEN]: jsonContent(ErrorResponseDTO, "Forbidden"),
        [HttpStatusCodes.NOT_FOUND]: jsonContent(ErrorResponseDTO, "Not found"),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
          ErrorResponseDTO,
          "Internal error",
        ),
      },
    }),
    permissionGuard({ jobVacancy: ["delete"] }),
    async (c) => {
      try {
        const { id } = c.req.param();
        const userId = c.var.checkedUser.id;
        const userRole = c.var.checkedUser.role;
        const vacancy = await JobVacancyOperations.findRawById(id);
        if (!vacancy) {
          return c.json(
            {
              code: ErrorCode.VACANCY_NOT_FOUND,
              message: "Job vacancy not found",
            },
            404 as ContentfulStatusCode,
          );
        }
        if (userRole === "author" && vacancy.authorId !== userId) {
          return c.json(
            { code: ErrorCode.FORBIDDEN, message: "Forbidden" },
            403 as ContentfulStatusCode,
          );
        }
        const deleted = await JobVacancyOperations.delete(id);
        if (!deleted) {
          return c.json(
            {
              code: ErrorCode.VACANCY_NOT_FOUND,
              message: "Job vacancy not found",
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.body(null, HttpStatusCodes.NO_CONTENT);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  );

export default router;
