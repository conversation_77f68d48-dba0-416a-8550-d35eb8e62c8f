import { describe<PERSON>oute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import z from "zod";

import { selectUserSchema } from "@/db/schema";
import { auth } from "@/lib/auth";
import { API_TAGS } from "@/lib/constants";
import { AppError, ErrorCode, mapErrorCodeToStatus } from "@/lib/errors";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { commonErrorResponses } from "@/lib/responses";
import { listUsersDTO } from "@/lib/schemas";
import {
  paginationParamsSchema,
  patchUserSchema,
  userFilterSchema,
} from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import type { ContentfulStatusCode } from "hono/utils/http-status";
import {
  listJobsQueryDTO,
  listJobsWithAuthorDTO,
} from "../job-vacancies/job-vacancies.dtos";
import { JobVacancyOperations } from "../job-vacancies/job-vacancies.operations";
import { createUserSchema, deleteAccountSchema } from "./users.dtos";
import { UsersOperations } from "./users.operations";

// Define allowed fields for the fields parameter
const allowedUserFields = z.enum([
  "id",
  "name",
  "description",
  "email",
  "image",
  "phoneNumber",
  "dateOfBirth",
  "gender",
  "role",
  "createdAt",
  "updatedAt",
]);

const router = createRouter()
  .use(authGuard)
  .get(
    "/",
    describeRoute({
      title: "List Users",
      description:
        "Get a paginated and filterable list of all users.\n- Access: Authenticated users with the 'user:list' permission (admin, editor).\n- Admins and editors: Can view all users.\n- Supports filtering, field selection, and filtering for users with published vacancies.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(listUsersDTO, "List of users"),
        ...commonErrorResponses(401, 403, 500),
      },
    }),
    permissionGuard({ user: ["list"] }),
    validator(
      "query",
      paginationParamsSchema
        .merge(userFilterSchema)
        .extend({
          hasVacancies: z
            .boolean()
            .optional()
            .describe(
              "Filter to include users who have published job vacancies.",
            ),
          fields: z
            .string()
            .optional()
            .describe(
              "Comma-separated list of fields to return (e.g., id,name).",
            )
            .transform((val) => {
              if (!val) return undefined;
              const fieldArray = val
                .split(",")
                .map((s) => s.trim())
                .filter(Boolean);
              // Validate each field against the allowed enum
              for (const field of fieldArray) {
                allowedUserFields.parse(field); // This will throw if an invalid field is present
              }
              return fieldArray; // Return as string[]
            }),
        })
        .optional(),
    ),
    async (c) => {
      try {
        const query = c.req.valid("query") || {};
        const result = await UsersOperations.findMany(query);
        return c.json(result, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/profile",
    describeRoute({
      title: "Get Current User",
      description:
        "Get the current user's information.\n- Access: Authenticated users.\n- Returns the full user profile for the current user.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectUserSchema,
          "Current user information",
        ),
        ...commonErrorResponses(401, 500),
      },
    }),
    async (c) => {
      try {
        const user = await UsersOperations.findById(c.var.checkedUser.id);
        return c.json(user, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .post(
    "/",
    describeRoute({
      title: "Create User",
      description:
        "Create a new user.\n- Access: Authenticated users with the 'user:create' permission (admin, editor).\n- The created user can have any role (admin, author, editor).\n- Returns the created user.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          selectUserSchema,
          "Created user",
        ),
        ...commonErrorResponses(401, 403, 422, 500),
      },
    }),
    permissionGuard({ user: ["create"] }),
    validator(
      "json",
      createUserSchema.extend({ role: z.enum(["admin", "author", "editor"]) }),
    ),
    async (c) => {
      try {
        const data = c.req.valid("json");
        const result = await auth.api.createUser({
          body: {
            ...data,
          },
          headers: c.req.raw.headers,
        });

        return c.json(result.user, HttpStatusCodes.CREATED);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/profile/job-vacancies",
    describeRoute({
      title: "List Own Job Vacancies",
      description:
        "List all job vacancies created by the current user.\n- Access: Authenticated users.\n- Returns all job vacancies authored by the current user, including author info.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          listJobsWithAuthorDTO,
          "List of user's job vacancies with author info",
        ),
        ...commonErrorResponses(401, 500),
      },
    }),
    validator(
      "query",
      listJobsQueryDTO
        .extend({
          status: z
            .enum(["published", "archived", "draft", "pending"])
            .optional(),
        })
        .optional(),
    ),
    async (c) => {
      try {
        const query = c.req.valid("query");
        const userId = c.var.checkedUser.id;
        const vacancies = await JobVacancyOperations.findByAuthor(
          userId,
          query,
        );
        return c.json(vacancies, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/:id",
    describeRoute({
      title: "Get User by ID",
      description:
        "Get a user by their ID.\n- Access: Authenticated users with the 'user:list' permission (admin, editor).\n- Admins and editors: Can view any user.\n- Returns user details, or 404 if not found.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectUserSchema.pick({
            id: true,
            name: true,
            description: true,
            email: true,
            image: true,
            phoneNumber: true,
            dateOfBirth: true,
            gender: true,
            role: true,
            createdAt: true,
            updatedAt: true,
          }),
          "User details",
        ),
        ...commonErrorResponses(401, 403, 404, 500),
      },
    }),
    permissionGuard({ user: ["list"] }),
    validator("param", z.object({ id: z.string() })),
    async (c) => {
      try {
        const { id } = c.req.valid("param");
        const found = await UsersOperations.findById(id);
        if (!found) {
          return c.json(
            {
              code: ErrorCode.USER_NOT_FOUND,
              message: `User with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.json(found, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .patch(
    "/profile",
    describeRoute({
      title: "Update Current User",
      description:
        "Update the current user's information.\n- Access: Authenticated users.\n- Returns the updated user profile.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(selectUserSchema, "Updated user"),
        ...commonErrorResponses(401, 403, 422, 500),
      },
    }),
    validator("json", patchUserSchema),
    async (c) => {
      try {
        const updates = c.req.valid("json");
        const updated = await UsersOperations.patch(
          c.var.checkedUser.id,
          updates,
        );
        return c.json(updated, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .delete(
    "/profile",
    describeRoute({
      title: "Delete Current User Account",
      description:
        "Delete the current user's account with password verification.\n- Access: Authenticated users.\n- Archives all published vacancies, deletes draft vacancies, and anonymizes user data.\n- Returns confirmation and deletion details.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.object({
            success: z.boolean(),
            deletionReason: z.string(),
            deletedAt: z.date(),
            message: z.string(),
          }),
          "Account deletion confirmation",
        ),
        ...commonErrorResponses(401, 422, 500),
      },
    }),
    validator("json", deleteAccountSchema),
    async (c) => {
      try {
        const { password, deletionReason } = c.req.valid("json");
        const userId = c.var.checkedUser.id;

        const result = await UsersOperations.deleteAccount(
          userId,
          password,
          deletionReason,
        );
        return c.json(
          {
            success: result.success,
            deletionReason: result.deletionReason,
            deletedAt: result.deletedAt,
            message:
              "Account has been successfully deleted and data anonymized.",
          },
          HttpStatusCodes.OK,
        );
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .patch(
    "/:id",
    describeRoute({
      title: "Update User",
      description:
        "Update a user by their ID.\n- Access: Authenticated users with the 'user:set-password' permission (admin, editor).\n- Admins and editors: Can update any user.\n- Returns the updated user, or 404 if not found.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(selectUserSchema, "Updated user"),
        ...commonErrorResponses(401, 403, 404, 422, 500),
      },
    }),
    permissionGuard({ user: ["set-password"] }),
    validator("param", z.object({ id: z.string() })),
    validator(
      "json",
      patchUserSchema.extend({
        role: z.enum(["admin", "author", "editor"]).optional(),
      }),
    ),
    async (c) => {
      try {
        const { id } = c.req.valid("param");
        const updates = c.req.valid("json");
        const updated = await UsersOperations.patch(id, updates);
        if (!updated) {
          return c.json(
            {
              code: ErrorCode.USER_NOT_FOUND,
              message: `User with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.json(updated, HttpStatusCodes.OK);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .delete(
    "/:id",
    describeRoute({
      title: "Delete User",
      description:
        "Delete a user by their ID.\n- Access: Authenticated users with the 'user:delete' permission (admin, editor).\n- Admins and editors: Can delete any user.\n- Returns 204 on success, 404 if not found, or 403 if not permitted.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: { description: "User deleted" },
        ...commonErrorResponses(401, 403, 404, 500),
      },
    }),
    permissionGuard({ user: ["delete"] }),
    validator("param", z.object({ id: z.string() })),
    async (c) => {
      try {
        const { id } = c.req.valid("param");
        const deleted = await UsersOperations.delete(id);
        if (!deleted) {
          return c.json(
            {
              code: ErrorCode.USER_NOT_FOUND,
              message: `User with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }
        return c.body(null, HttpStatusCodes.NO_CONTENT);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        console.error(err);
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  );

export default router;
