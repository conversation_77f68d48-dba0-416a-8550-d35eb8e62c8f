import db from "@/db";
import { insertUserSchema, selectUserSchema, user } from "@/db/schema";
import { auth } from "@/lib/auth";
import { API_TAGS } from "@/lib/constants";
import { AppError, ErrorCode, mapErrorCodeToStatus } from "@/lib/errors";
import { createRouter, json<PERSON>ontent, validator } from "@/lib/helpers";
import { commonErrorResponses } from "@/lib/responses";
import { AuthorDTO, listAuthorsDTO } from "@/lib/schemas";
import { paginationParamsSchema } from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { count, eq } from "drizzle-orm";
import { describeRoute } from "hono-openapi";
import type { ContentfulStatusCode } from "hono/utils/http-status";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";

const createUserSchema = insertUserSchema
  .omit({
    id: true,
    role: true,
    emailVerified: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    banned: true,
    banReason: true,
    banExpires: true,
  })
  .extend({
    password: z.string().min(8),
  });

const router = createRouter()
  .use(authGuard)
  .get(
    "/",
    describeRoute({
      title: "List Authors",
      description:
        "Get a paginated list of all authors.\n- Access: Authenticated users with the 'author:read' permission (admin, editor, author).\n- Admins, editors, authors: Can view all authors.\n- The response is paginated and includes author details and bio.",
      tags: [API_TAGS.AUTHORS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(listAuthorsDTO, "List of authors"),
      },
    }),
    permissionGuard({ author: ["read"] }),
    validator("query", paginationParamsSchema.optional()),
    async (c) => {
      try {
        const { page = 1, limit } = c.req.valid("query") || {};
        const offset = limit ? (page - 1) * limit : undefined;
        const [usersList, [{ total }]] = await Promise.all([
          db.query.user.findMany({
            where: { role: "author" },
            limit,
            offset,
            orderBy: { createdAt: "desc" },
            with: {
              authorDetails: true,
            },
            columns: {
              id: true,
              name: true,
              email: true,
              image: true,
              phoneNumber: true,
              dateOfBirth: true,
              gender: true,
              role: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
          db
            .select({ total: count() })
            .from(user)
            .where(eq(user.role, "author")),
        ]);
        const items = usersList.map(({ authorDetails, ...u }) => ({
          ...u,
          bio: authorDetails?.bio ?? null,
        }));
        return c.json(
          {
            items,
            total,
            page,
            limit,
            pages: limit ? Math.ceil(total / limit) : undefined,
          },
          HttpStatusCodes.OK,
        );
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .get(
    "/:id",
    describeRoute({
      title: "Get Author by ID",
      description:
        "Get a single author by user ID.\n- Access: Authenticated users with the 'author:read' permission (admin, editor, author).\n- Admins, editors, authors: Can view any author.\n- Returns author details and bio, or 404 if not found or not an author.",
      tags: [API_TAGS.AUTHORS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(AuthorDTO, "Author details"),
        ...commonErrorResponses(404, 500),
      },
    }),
    permissionGuard({ author: ["read"] }),
    async (c) => {
      try {
        const { id } = c.req.param();
        const found = await db.query.user.findFirst({
          where: { id },
          with: { authorDetails: true },
          columns: {
            id: true,
            name: true,
            email: true,
            image: true,
            phoneNumber: true,
            dateOfBirth: true,
            gender: true,
            role: true,
            createdAt: true,
            updatedAt: true,
          },
        });
        if (!found || found.role !== "author") {
          return c.json(
            {
              code: ErrorCode.AUTHOR_NOT_FOUND,
              message: `Author with ID ${id} not found`,
            },
            404 as ContentfulStatusCode,
          );
        }
        const { authorDetails, ...u } = found;
        return c.json(
          {
            ...u,
            bio: authorDetails?.bio ?? null,
          },
          HttpStatusCodes.OK,
        );
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  )
  .post(
    "/",
    describeRoute({
      title: "Create Author",
      description:
        "Create a new author user.\n- Access: Authenticated users with the 'author:create' permission (admin, editor).\n- The created user will have the 'author' role and email marked as verified.\n- Returns the created author user.",
      tags: [API_TAGS.AUTHORS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          selectUserSchema,
          "Created author user",
        ),
        ...commonErrorResponses(401, 403, 422, 500),
      },
    }),
    permissionGuard({ author: ["create"] }),
    validator("json", createUserSchema),
    async (c) => {
      try {
        const data = c.req.valid("json");
        const result = await auth.api.createUser({
          body: {
            ...data,
            role: "author",
            emailVerified: true,
          },
          headers: c.req.raw.headers,
        });

        return c.json(result.user, HttpStatusCodes.CREATED);
      } catch (err) {
        if (err instanceof AppError) {
          return c.json(
            { code: err.code, message: err.message, details: err.details },
            mapErrorCodeToStatus(err.code) as ContentfulStatusCode,
          );
        }
        return c.json(
          { code: ErrorCode.INTERNAL_ERROR, message: "Internal server error" },
          500 as ContentfulStatusCode,
        );
      }
    },
  );

export default router;
