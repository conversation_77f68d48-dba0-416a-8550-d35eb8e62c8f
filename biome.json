{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "always", "trailingCommas": "all"}}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "error", "noUnusedImports": "error"}, "suspicious": {"noConsoleLog": "warn", "noExportsInTest": "off"}, "style": {"useTemplate": "error", "useConst": "error", "noNonNullAssertion": "off", "useSingleVarDeclarator": "off"}, "complexity": {"noStaticOnlyClass": "off"}}, "ignore": ["**/migrations/*", "./src/db/migrations/**"]}}